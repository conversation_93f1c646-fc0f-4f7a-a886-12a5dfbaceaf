#!/usr/bin/env python3
"""
Verification script for batch operations implementation.
This script verifies that all components are properly implemented without requiring <PERSON><PERSON><PERSON>.
"""

import os
import sys
import importlib.util
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a file exists and report status."""
    if os.path.exists(filepath):
        print(f"✓ {description}: {filepath}")
        return True
    else:
        print(f"✗ {description}: {filepath} (NOT FOUND)")
        return False

def check_module_imports(filepath, expected_functions):
    """Check if a module can be imported and has expected functions."""
    try:
        spec = importlib.util.spec_from_file_location("test_module", filepath)
        module = importlib.util.module_from_spec(spec)
        
        # Don't execute the module, just check if it can be loaded
        with open(filepath, 'r') as f:
            content = f.read()
        
        missing_functions = []
        for func in expected_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"✗ {filepath}: Missing functions: {missing_functions}")
            return False
        else:
            print(f"✓ {filepath}: All expected functions found")
            return True
            
    except Exception as e:
        print(f"✗ {filepath}: Import error: {e}")
        return False

def check_integration_updates(filepath, expected_patterns):
    """Check if integration updates are present in the main file."""
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        missing_patterns = []
        for pattern in expected_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"✗ {filepath}: Missing integration patterns: {missing_patterns}")
            return False
        else:
            print(f"✓ {filepath}: All integration patterns found")
            return True
            
    except Exception as e:
        print(f"✗ {filepath}: Read error: {e}")
        return False

def main():
    """Run verification checks."""
    print("Database Batch Operations Implementation Verification")
    print("=" * 60)
    
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Check core files exist
    print("\n1. Checking Core Files:")
    files_check = [
        (os.path.join(base_dir, "db_batch_operations.py"), "Batch Operations Module"),
        (os.path.join(base_dir, "batch_config.py"), "Configuration Module"),
        (os.path.join(base_dir, "test_batch_operations.py"), "Test Script"),
        (os.path.join(base_dir, "README_BATCH_OPERATIONS.md"), "Documentation"),
        (os.path.join(base_dir, "telegram_integration.py"), "Main Integration File")
    ]
    
    files_ok = all(check_file_exists(filepath, desc) for filepath, desc in files_check)
    
    # Check batch operations module
    print("\n2. Checking Batch Operations Module:")
    batch_ops_file = os.path.join(base_dir, "db_batch_operations.py")
    expected_batch_functions = [
        "class DatabaseBatchManager",
        "def get_batch_manager",
        "async def create_message_batch",
        "async def create_room_batch", 
        "async def create_agent_batch",
        "async def update_message_status_batch",
        "async def bulk_validate_messages",
        "def load_seen_messages_optimized"
    ]
    
    batch_ops_ok = check_module_imports(batch_ops_file, expected_batch_functions)
    
    # Check configuration module
    print("\n3. Checking Configuration Module:")
    config_file = os.path.join(base_dir, "batch_config.py")
    expected_config_functions = [
        "def get_batch_config",
        "def update_batch_config",
        "def get_optimal_batch_size",
        "def is_feature_enabled",
        "def load_config_from_env"
    ]
    
    config_ok = check_module_imports(config_file, expected_config_functions)
    
    # Check integration updates
    print("\n4. Checking Integration Updates:")
    integration_file = os.path.join(base_dir, "telegram_integration.py")
    expected_integration_patterns = [
        "from .db_batch_operations import",
        "BATCH_OPERATIONS_AVAILABLE",
        "use_batching: bool = True",
        "immediate_commit: bool = False",
        "load_seen_messages_optimized",
        "create_message_batch",
        "update_message_status_batch"
    ]
    
    integration_ok = check_integration_updates(integration_file, expected_integration_patterns)
    
    # Check configuration values
    print("\n5. Checking Configuration Values:")
    try:
        sys.path.append(base_dir)
        from batch_config import get_batch_config
        config = get_batch_config()
        
        required_config_sections = ['batch_sizes', 'timing', 'performance', 'features']
        config_sections_ok = all(section in config for section in required_config_sections)
        
        if config_sections_ok:
            print("✓ Configuration structure is valid")
            print(f"  - Default batch size: {config['batch_sizes']['default']}")
            print(f"  - Auto commit interval: {config['timing']['auto_commit_interval']}s")
            print(f"  - Max worker threads: {config['performance']['max_worker_threads']}")
            print(f"  - Message batching enabled: {config['features']['enable_message_batching']}")
        else:
            print("✗ Configuration structure is invalid")
            config_sections_ok = False
            
    except Exception as e:
        print(f"✗ Configuration check failed: {e}")
        config_sections_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY:")
    
    checks = [
        ("Core Files", files_ok),
        ("Batch Operations Module", batch_ops_ok),
        ("Configuration Module", config_ok),
        ("Integration Updates", integration_ok),
        ("Configuration Values", config_sections_ok)
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"  {check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("\nThe batch operations implementation is ready for use.")
        print("\nNext steps:")
        print("1. Test in your Frappe environment")
        print("2. Monitor performance improvements")
        print("3. Adjust batch sizes based on your workload")
        print("4. Enable performance logging to track improvements")
    else:
        print("❌ SOME CHECKS FAILED!")
        print("\nPlease review the failed checks above and fix any issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
