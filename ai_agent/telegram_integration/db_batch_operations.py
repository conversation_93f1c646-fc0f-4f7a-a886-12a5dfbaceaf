import frappe
import asyncio
import time
import json
import logging
import os
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from concurrent.futures import Thread<PERSON><PERSON>Executor
from collections import defaultdict
import threading

# Import configuration
try:
    from .batch_config import (
        get_batch_config,
        get_optimal_batch_size,
        is_feature_enabled,
        DEFAULT_BATCH_SIZE,
        AUTO_COMMIT_INTERVAL,
        MAX_WORKER_THREADS
    )
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    DEFAULT_BATCH_SIZE = 50
    AUTO_COMMIT_INTERVAL = 5.0
    MAX_WORKER_THREADS = 4

logger = logging.getLogger(__name__)

class DatabaseBatchManager:
    """
    Manages batched database operations to optimize performance by reducing
    the number of individual commits and database round trips.
    """

    def __init__(self, batch_size: int = None, auto_commit_interval: float = None):
        # Use configuration if available
        if CONFIG_AVAILABLE:
            config = get_batch_config()
            self.batch_size = batch_size or config['batch_sizes']['default']
            self.auto_commit_interval = auto_commit_interval or config['timing']['auto_commit_interval']
            max_workers = config['performance']['max_worker_threads']
            self.enable_performance_logging = config['performance']['enable_performance_logging']
        else:
            self.batch_size = batch_size or DEFAULT_BATCH_SIZE
            self.auto_commit_interval = auto_commit_interval or AUTO_COMMIT_INTERVAL
            max_workers = MAX_WORKER_THREADS
            self.enable_performance_logging = False

        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # Batch storage
        self.message_batch = []
        self.room_batch = []
        self.agent_batch = []
        self.update_batch = []

        # Tracking
        self.last_commit_time = time.time()
        self.batch_lock = threading.Lock()
        self.pending_operations = 0

        # Start auto-commit task
        self._auto_commit_task = None
        self._start_auto_commit()

    def _start_auto_commit(self):
        """Start the auto-commit background task."""
        if self._auto_commit_task is None or self._auto_commit_task.done():
            self._auto_commit_task = asyncio.create_task(self._auto_commit_loop())

    async def _auto_commit_loop(self):
        """Background task that commits batches periodically."""
        while True:
            try:
                await asyncio.sleep(self.auto_commit_interval)
                if self.pending_operations > 0:
                    await self.commit_all_batches()
            except Exception as e:
                logger.error(f"Error in auto-commit loop: {e}")

    async def add_message_to_batch(self, message_data: Dict[str, Any]) -> None:
        """Add a message to the batch for later insertion."""
        with self.batch_lock:
            self.message_batch.append(message_data)
            self.pending_operations += 1

        if len(self.message_batch) >= self.batch_size:
            await self.commit_message_batch()

    async def add_room_to_batch(self, room_data: Dict[str, Any]) -> None:
        """Add a room to the batch for later insertion."""
        with self.batch_lock:
            self.room_batch.append(room_data)
            self.pending_operations += 1

        if len(self.room_batch) >= self.batch_size:
            await self.commit_room_batch()

    async def add_agent_to_batch(self, agent_data: Dict[str, Any]) -> None:
        """Add an agent to the batch for later insertion."""
        with self.batch_lock:
            self.agent_batch.append(agent_data)
            self.pending_operations += 1

        if len(self.agent_batch) >= self.batch_size:
            await self.commit_agent_batch()

    async def add_update_to_batch(self, doctype: str, name: str, updates: Dict[str, Any]) -> None:
        """Add a document update to the batch."""
        with self.batch_lock:
            self.update_batch.append({
                'doctype': doctype,
                'name': name,
                'updates': updates
            })
            self.pending_operations += 1

        if len(self.update_batch) >= self.batch_size:
            await self.commit_update_batch()

    async def commit_message_batch(self) -> List[str]:
        """Commit all pending messages in a single transaction."""
        if not self.message_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.message_batch.copy()
            self.message_batch.clear()

        return await self._execute_in_thread(self._commit_messages_sync, batch_to_process)

    async def commit_room_batch(self) -> List[str]:
        """Commit all pending rooms in a single transaction."""
        if not self.room_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.room_batch.copy()
            self.room_batch.clear()

        return await self._execute_in_thread(self._commit_rooms_sync, batch_to_process)

    async def commit_agent_batch(self) -> List[str]:
        """Commit all pending agents in a single transaction."""
        if not self.agent_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.agent_batch.copy()
            self.agent_batch.clear()

        return await self._execute_in_thread(self._commit_agents_sync, batch_to_process)

    async def commit_update_batch(self) -> int:
        """Commit all pending updates in a single transaction."""
        if not self.update_batch:
            return 0

        with self.batch_lock:
            batch_to_process = self.update_batch.copy()
            self.update_batch.clear()

        return await self._execute_in_thread(self._commit_updates_sync, batch_to_process)

    async def commit_all_batches(self) -> Dict[str, Any]:
        """Commit all pending batches."""
        results = {}

        if self.message_batch:
            results['messages'] = await self.commit_message_batch()
        if self.room_batch:
            results['rooms'] = await self.commit_room_batch()
        if self.agent_batch:
            results['agents'] = await self.commit_agent_batch()
        if self.update_batch:
            results['updates'] = await self.commit_update_batch()

        with self.batch_lock:
            self.pending_operations = 0
            self.last_commit_time = time.time()

        return results

    async def _execute_in_thread(self, func, *args):
        """Execute a synchronous function in a thread pool."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, func, *args)

    def _commit_messages_sync(self, messages: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of messages."""
        inserted_names = []

        try:
            for msg_data in messages:
                try:
                    message = frappe.new_doc("Message")

                    # Set all message fields
                    message.message_id = msg_data.get('message_id')
                    message.room_id = msg_data.get('room_id')
                    message.sender_id = msg_data.get('sender_id')
                    message.sender_name = msg_data.get('sender_name', 'UnknownUser')
                    message.receiver = msg_data.get('receiver_id', msg_data.get('sender_id'))
                    message.agent = msg_data.get('agent_name')
                    message.text = msg_data.get('text', '')
                    message.status = msg_data.get('status', 'received')
                    message.is_response_template = msg_data.get('is_response_template', False)
                    message.reply_to_message_id = msg_data.get('reply_to_message_id')

                    if msg_data.get('media_url'):
                        message.media_url = msg_data['media_url']
                    if msg_data.get('media_type'):
                        message.media_type = msg_data['media_type']
                    if msg_data.get('timestamp'):
                        message.timestamp = msg_data['timestamp']

                    message.insert(ignore_permissions=True)
                    inserted_names.append(message.name)

                except Exception as e:
                    logger.error(f"Error inserting message {msg_data.get('message_id')}: {e}")
                    continue

            # Single commit for all messages
            frappe.db.commit()
            logger.info(f"Batch committed {len(inserted_names)} messages successfully")

        except Exception as e:
            logger.error(f"Error in batch message commit: {e}")
            frappe.db.rollback()
            raise

        return inserted_names

    def _commit_rooms_sync(self, rooms: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of rooms."""
        inserted_names = []

        try:
            for room_data in rooms:
                try:
                    # Check if room already exists
                    existing_rooms = frappe.get_list(
                        "Room",
                        filters={"room_id": room_data.get('room_id')},
                        fields=["name"],
                        ignore_permissions=True
                    )

                    if existing_rooms:
                        # Update existing room if needed
                        room = frappe.get_doc("Room", existing_rooms[0]["name"])
                        if room.title != room_data.get('title') and room_data.get('title'):
                            room.title = room_data['title']
                            room.save(ignore_permissions=True)
                        inserted_names.append(room.name)
                    else:
                        # Create new room
                        room = frappe.new_doc("Room")
                        room.room_id = room_data.get('room_id')
                        room.chat_id = room_data.get('chat_id')
                        room.sender_id = room_data.get('sender_id', room_data.get('chat_id'))
                        room.agent_name = room_data.get('agent_name')
                        room.title = room_data.get('title', f"Chat_{room_data.get('chat_id')}_{room_data.get('agent_name')}")

                        room.insert(ignore_permissions=True, ignore_if_duplicate=True)
                        inserted_names.append(room.name)

                except Exception as e:
                    logger.error(f"Error inserting room {room_data.get('room_id')}: {e}")
                    continue

            # Single commit for all rooms
            frappe.db.commit()
            logger.info(f"Batch committed {len(inserted_names)} rooms successfully")

        except Exception as e:
            logger.error(f"Error in batch room commit: {e}")
            frappe.db.rollback()
            raise

        return inserted_names

    def _commit_agents_sync(self, agents: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of agents."""
        inserted_names = []

        try:
            for agent_data in agents:
                try:
                    user_id = agent_data.get('user_id')
                    agent_name = agent_data.get('agent_name')

                    existing_agent = frappe.get_list(
                        "Agents",
                        filters={"user_id": user_id},
                        fields=["name", "agent_name"]
                    )

                    if existing_agent:
                        # Update existing agent
                        agent_doc = frappe.get_doc("Agents", existing_agent[0]["name"])
                        if agent_doc.agent_name != agent_name:
                            agent_doc.agent_name = agent_name
                            agent_doc.title = agent_name
                            agent_doc.save(ignore_permissions=True)
                        inserted_names.append(agent_doc.name)
                    else:
                        # Create new agent
                        agent = frappe.new_doc("Agents")
                        agent.user_id = user_id
                        agent.agent_name = agent_name
                        agent.title = agent_name
                        agent.insert(ignore_permissions=True)
                        inserted_names.append(agent.name)

                except Exception as e:
                    logger.error(f"Error inserting agent {agent_data.get('agent_name')}: {e}")
                    continue

            # Single commit for all agents
            frappe.db.commit()
            logger.info(f"Batch committed {len(inserted_names)} agents successfully")

        except Exception as e:
            logger.error(f"Error in batch agent commit: {e}")
            frappe.db.rollback()
            raise

        return inserted_names

    def _commit_updates_sync(self, updates: List[Dict[str, Any]]) -> int:
        """Synchronously commit a batch of document updates."""
        updated_count = 0

        try:
            for update_data in updates:
                try:
                    doctype = update_data['doctype']
                    name = update_data['name']
                    updates = update_data['updates']

                    frappe.db.set_value(doctype, name, updates)
                    updated_count += 1

                except Exception as e:
                    logger.error(f"Error updating {update_data.get('doctype')} {update_data.get('name')}: {e}")
                    continue

            # Single commit for all updates
            frappe.db.commit()
            logger.info(f"Batch committed {updated_count} updates successfully")

        except Exception as e:
            logger.error(f"Error in batch update commit: {e}")
            frappe.db.rollback()
            raise

        return updated_count

# Global batch manager instance
_batch_manager = None

def get_batch_manager() -> DatabaseBatchManager:
    """Get or create the global batch manager instance."""
    global _batch_manager
    if _batch_manager is None:
        _batch_manager = DatabaseBatchManager()
    return _batch_manager

async def bulk_validate_messages(message_ids: List[str]) -> Set[str]:
    """
    Efficiently validate which message IDs already exist in the database.
    Replaces the N+1 query pattern in load_seen_messages.
    """
    if not message_ids:
        return set()

    try:
        # Single query to check all message IDs
        existing_messages = frappe.get_list(
            "Message",
            filters={"message_id": ["in", message_ids]},
            fields=["message_id"],
            ignore_permissions=True
        )
        return {msg["message_id"] for msg in existing_messages}
    except Exception as e:
        logger.error(f"Error in bulk message validation: {e}")
        return set()

# Optimized batch functions for common operations

async def create_message_batch(
    chat_id: str,
    room_id: Optional[str],
    message_id: str,
    sender_id: str,
    sender_name: str,
    text: Optional[str] = None,
    media_url: Optional[str] = None,
    agent_name: Optional[str] = None,
    media_type: Optional[str] = None,
    timestamp: Optional[datetime] = None,
    receiver_id: Optional[str] = None,
    is_response_template: bool = False,
    reply_to_message_id: Optional[str] = None,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_message that uses batching.
    Returns the message name if immediately committed, None if batched.
    """
    batch_manager = get_batch_manager()

    message_data = {
        'message_id': message_id,
        'room_id': room_id,
        'sender_id': sender_id,
        'sender_name': sender_name,
        'receiver_id': receiver_id or sender_id,
        'agent_name': agent_name,
        'text': text or '',
        'status': 'received' if receiver_id and not is_response_template else 'sent',
        'is_response_template': is_response_template,
        'reply_to_message_id': reply_to_message_id,
        'media_url': media_url,
        'media_type': media_type,
        'timestamp': timestamp
    }

    if immediate_commit:
        # Force immediate commit for this message
        results = await batch_manager._execute_in_thread(
            batch_manager._commit_messages_sync, [message_data]
        )
        return results[0] if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_message_to_batch(message_data)
        return None

async def create_room_batch(
    chat_id: str,
    title: str,
    agent_name: str,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_or_update_room that uses batching.
    """
    batch_manager = get_batch_manager()

    room_id = f"{chat_id}:{agent_name}"
    room_data = {
        'room_id': room_id,
        'chat_id': chat_id,
        'sender_id': chat_id,
        'agent_name': agent_name,
        'title': title or f"Chat_{chat_id}_{agent_name}"
    }

    if immediate_commit:
        # Force immediate commit for this room
        results = await batch_manager._execute_in_thread(
            batch_manager._commit_rooms_sync, [room_data]
        )
        return room_id if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_room_to_batch(room_data)
        return room_id

async def create_agent_batch(
    user_id: str,
    agent_name: str,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_or_update_agent that uses batching.
    """
    batch_manager = get_batch_manager()

    agent_data = {
        'user_id': user_id,
        'agent_name': agent_name
    }

    if immediate_commit:
        # Force immediate commit for this agent
        results = await batch_manager._execute_in_thread(
            batch_manager._commit_agents_sync, [agent_data]
        )
        return results[0] if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_agent_to_batch(agent_data)
        return None

async def update_message_status_batch(
    message_name: str,
    status: str,
    message_id: Optional[str] = None,
    immediate_commit: bool = False
) -> None:
    """
    Batch update message status to avoid individual commits.
    """
    batch_manager = get_batch_manager()

    updates = {'status': status}
    if message_id:
        updates['message_id'] = message_id

    if immediate_commit:
        # Force immediate commit
        await batch_manager._execute_in_thread(
            batch_manager._commit_updates_sync,
            [{'doctype': 'Message', 'name': message_name, 'updates': updates}]
        )
    else:
        # Add to batch
        await batch_manager.add_update_to_batch('Message', message_name, updates)

def load_seen_messages_optimized(seen_file: str) -> Dict[str, Any]:
    """
    Optimized version of load_seen_messages that uses bulk validation
    instead of N+1 queries.
    """
    path = seen_file
    if not os.path.exists(path):
        return {"messages": {}, "last_processed": {}, "_file": path}

    try:
        with open(path, 'r') as f:
            data = json.load(f)
            seen_messages = data.get("messages", {})
            last_processed = data.get("last_processed", {})

        if not seen_messages:
            return {"messages": {}, "last_processed": last_processed, "_file": path}

        # Use bulk validation instead of individual queries
        message_ids = list(seen_messages.keys())

        # Run the bulk validation in a thread to avoid blocking
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            existing_ids = loop.run_until_complete(bulk_validate_messages(message_ids))
        finally:
            loop.close()

        # Filter to only include existing messages
        valid_seen = {key: val for key, val in seen_messages.items() if key in existing_ids}

        return {"messages": valid_seen, "last_processed": last_processed, "_file": path}

    except Exception as e:
        logger.error(f"Error loading seen messages: {e}")
        return {"messages": {}, "last_processed": {}, "_file": path}
